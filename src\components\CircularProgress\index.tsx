import React from 'react';
import { View } from '@tarojs/components';
import { CircleProgress } from '@nutui/nutui-react-taro';
import './index.scss';

interface CircularProgressProps {
  size?: number;
  progress: number;
  strokeWidth?: number;
  strokeColor?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  size = 100,
  progress = 0,
  strokeWidth = 8,
  strokeColor = '#1989fa',
  children,
  className = '',
  style,
}) => {
  // 计算合适的radius，让进度条显示得更大一些
  const radius = size * 0.8; // 使用size的45%作为半径，让进度条更大

  return (
    <View className={`circular-progress ${className}`} style={style}>
      <CircleProgress
        progress={progress}
        strokeWidth={strokeWidth}
        circleColor={strokeColor}
        radius={radius}
      >
        {children}
      </CircleProgress>
    </View>
  );
};

export default CircularProgress;
