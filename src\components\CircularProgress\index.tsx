import React from 'react';
import { View } from '@tarojs/components';
import { CircleProgress } from '@nutui/nutui-react-taro';
import './index.scss';

interface CircularProgressProps {
  size?: number;
  progress: number;
  strokeWidth?: number;
  strokeColor?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  size = 100,
  progress = 0,
  strokeWidth = 8,
  strokeColor = '#1989fa',
  children,
  className = '',
  style,
}) => {
  return (
    <View
      className={`circular-progress ${className}`}
      style={{ width: `${size}px`, height: `${size}px`, ...style }}
    >
      <CircleProgress
        progress={progress}
        strokeWidth={strokeWidth}
        circleColor={strokeColor}
        radius={100}
        // style={{ width: `${size}px`, height: `${size}px` }}
      >
        {children}
      </CircleProgress>
    </View>
  );
};

export default CircularProgress;
