import React, { useEffect, useRef } from 'react';
import { View, Text, Canvas } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.scss';

interface CircularProgressProps {
  size?: number;
  progress: number;
  strokeWidth?: number;
  strokeColor?: string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  size = 100,
  progress = 0,
  strokeWidth = 8,
  strokeColor = '#1989fa',
  children,
  className = '',
  style,
}) => {
  const canvasRef = useRef<any>(null);
  const isH5 = process.env.TARO_ENV === 'h5';

  useEffect(() => {
    if (!isH5 && canvasRef.current) {
      drawCanvas();
    }
  }, [progress, size, strokeWidth, strokeColor]);

  const drawCanvas = () => {
    Taro.createSelectorQuery()
      .select('#circular-canvas')
      .fields({ node: true, size: true })
      .exec(res => {
        if (!res[0]) return;

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        const dpr = Taro.getWindowInfo().pixelRatio;
        // 使用2倍dpr提高清晰度
        const scale = dpr * 5;
        canvas.width = size * scale;
        canvas.height = size * scale;
        ctx.scale(scale, scale);
        // 设置抗锯齿
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        const radius = (size - strokeWidth) / 2;
        const center = size / 2;
        const startAngle = -Math.PI / 2;
        const endAngle = startAngle + (progress / 100) * 2 * Math.PI;

        // Clear canvas
        ctx.clearRect(0, 0, size, size);

        // Draw background circle
        ctx.beginPath();
        ctx.arc(center, center, radius, 0, 2 * Math.PI);
        ctx.lineWidth = strokeWidth;
        ctx.strokeStyle = '#ebedf0';
        ctx.lineCap = 'round';
        ctx.stroke();

        // Draw progress arc
        ctx.beginPath();
        ctx.arc(center, center, radius, startAngle, endAngle);
        ctx.lineWidth = strokeWidth;
        ctx.strokeStyle = progress == 0 ? 'transparent' : strokeColor;
        ctx.lineCap = 'round';
        ctx.stroke();
      });
  };

  // if (isH5) {
  //   const radius = (size - strokeWidth) / 2
  //   const circumference = 2 * Math.PI * radius
  //   const offset = circumference - (progress / 100) * circumference

  //   return (
  //     <View
  //       className={`circular-progress ${className}`}
  //       style={{ width: `${size}px`, height: `${size}px`, ...style }}
  //     >
  //       <svg className="circular-progress__svg" viewBox={`0 0 ${size} ${size}`}>
  //         <circle
  //           className="circular-progress__bg"
  //           cx={size / 2}
  //           cy={size / 2}
  //           r={radius}
  //           strokeWidth={strokeWidth}
  //           fill="none"
  //         />
  //         <circle
  //           className="circular-progress__bar"
  //           cx={size / 2}
  //           cy={size / 2}
  //           r={radius}
  //           strokeWidth={strokeWidth}
  //           fill="none"
  //           stroke={strokeColor}
  //           strokeDasharray={circumference}
  //           strokeDashoffset={offset}
  //           strokeLinecap="round"
  //           transform={`rotate(-90 ${size / 2} ${size / 2})`}
  //         />
  //     </svg>
  //     <View className="circular-progress__content">
  //       {children}
  //     </View>
  //     </View>
  //   )
  // }
  return (
    <View
      className={`circular-progress ${className}`}
      style={{ width: `${size}px`, height: `${size}px`, ...style }}
    >
      <Canvas
        id='circular-canvas'
        type='2d'
        ref={canvasRef}
        style={{
          width: `${size}%`,
          height: `${size}%`,
          // transform: 'scale(0.5)',
          // transformOrigin: '0 0'
        }}
      />
      <View className='circular-progress__content'>{children}</View>
    </View>
  );
};

export default CircularProgress;
