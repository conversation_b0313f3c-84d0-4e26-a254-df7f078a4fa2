import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Icon } from '@nutui/nutui-react-taro';
import Taro, {
  removeStorageSync,
  usePullDownRefresh,
  useReachBottom,
} from '@tarojs/taro';
import bluetoothService from '@/services/bluetoothService';
import { View, Image, Text } from '@tarojs/components';
import {
  CircleProgress,
  Cell,
  Switch,
  NoticeBar,
  Dialog,
  Overlay,
  Drag,
  Button,
} from '@nutui/nutui-react-taro';
import { inject, observer } from 'mobx-react';
import { getParseNotifyRecord, getStatus } from '@/config/notify';
import {
  getwxSendComC8,
  postBladeSystemUserDel,
  getBladeSystemUserAppId,
  getBatteryData,
  getBladeSystemUserValidatePhone,
  getDeviceVersion,
  getBatterAll,
  postBladeSystemUpBindEqu,
  postBladeSystemUpUnBindEqu,
  liftUserBindEqu,
  getBladeSystemUserCkEquNoExist,
  userBindDevice,
  saveBluetoothData,
} from '../../api/index';
import './index.scss';
import CircularProgress from '@/components/CircularProgress';
import HeaderNavBar from '@/components/HeaderNavBar';
import Icon1 from '@/assets/images/icon_dianya.png';
import Icon2 from '@/assets/images/icon_dianliu.png';
import Icon3 from '@/assets/images/icon_wendu.png';
import Icon4 from '@/assets/images/icon_xunhuan.png';
import refreshStore from '../../store/refreshStore';
// import Icon6 from '@/assets/images/icon_shebeizhuangtai.png'
import Icon8 from '@/assets/images/icon_shiyongshuoming.png';
import Icon9 from '@/assets/images/icon_shiyongshuoming.png';
import Icon10 from '@/assets/images/icon_shouhoufuwu.png';
import Icon11 from '@/assets/images/bluetooth.png';
import Icon12 from '@/assets/images/network.png';
import Icon13 from '@/assets/images/activeBluetooth.png';
import Icon14 from '@/assets/images/activeNetwork.png';
import OnOffPng from '@/assets/images/OnOff.png';
import Fire from '@/assets/images/fire.png';
import Electricity from '@/assets/images/electricity.png';
import ElectricityActive from '@/assets/images/electricityActive.png';
//electricityActive
import FireTwo from '@/assets/images/fireTwo.png';
import closeOne from '@/assets/images/closeBtnTwo.png';
import closeThree from '@/assets/images/closeBtnOne.png';
import battery from '@/assets/images/battery.png';
import unbind from '@/assets/images/unbind.png';
import binding from '@/assets/images/binding.png';
import arrows from '@/assets/images/arrows.png';
import leftClosePng from '@/assets/images/leftClose.png';
import rightClosePng from '@/assets/images/rightClose.png';
import NoPng from '@/assets/images/No.png';
import shebeimingcheng from '@/assets/images/icon_shebeimingcheng.png';
//icon_shebeimingcheng
import closeTwo from '@/assets/images/closeBtnTwo.png';
import { getCurrentDate } from '@/utils/todo';
import bluetoothLink from '../../store/bluetoothLink';
import { transform, debounce, delay } from 'lodash';

// import { HotModuleReplacementPlugin } from 'webpack'

const ErrorStatus = [
  {
    title: '压差过大',
  },
  {
    title: '电压低',
  },
  {
    title: '电压高',
  },
  {
    title: 'MOS过温',
  },
  {
    title: '温度高充电功能关闭',
  },
  {
    title: '温度低充电功能关闭',
  },
  {
    title: '温度高放电功能关闭',
  },
  {
    title: '温度低放电功能关闭',
  },
];

const clearEquData = {
  电池编号: '--',
  电流: '--',
  剩余电量: '--',
  累计循环放电次数: '--',
  预计使用时间: '',
  软件版本: '--',
  电池状态: '离线',
  硬件状态: '--',
  温度: [],
  电池电压: [],
  校验: '',
  告警: '',
};
//开机
const btnStyle1: React.CSSProperties = {
  borderRadius: '57px',
  fontSize: '14px',
  color: '#fff',
  width: '53px',
  height: '53px',
  lineHeight: '47px',
  backgroundColor: '#4EBBB4',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  top: '-2%',
  left: '15%',
};
//关机
const btnStyle2: React.CSSProperties = {
  borderRadius: '57px',
  fontSize: '14px',
  color: '#fff',
  width: '53px',
  height: '53px',
  lineHeight: '47px',
  backgroundColor: 'black',
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  top: '-2%',
  left: '13%',
};

const HomePage = observer(({ store }) => {
  let counterStore = store.counterStore.state;
  const phone = Taro.getStorageSync('phone');
  const refresh = Taro.getStorageSync('refresh');
  const seqNo = Taro.getStorageSync('seqNo');
  const isBluetoothLink = store.bluetoothLink.state; //蓝牙连接状态
  const BluetoothStore = store.BluetoothStore.state;
  const [cleanupFn, setCleanupFn] = useState(() => () => {});
  const [isActiveImg, setisActiveImg] = useState(true); //蓝牙
  const [isActiveImg2, setisActiveImg2] = useState(true); //mqtt
  const [visible1, setVisible1] = useState(false); //提示
  const [visible2, setVisible2] = useState(false); //提示
  const [visible3, setVisible3] = useState(false); //提示
  const [visible4, setVisible4] = useState(false); //
  const [visible5, setVisible5] = useState(false); //提示
  const [visible6, setVisible6] = useState(false); //提示
  const [visible7, setVisible7] = useState(false); //提示
  const [visible8, setVisible8] = useState(false); //提示
  const [visible9, setVisible9] = useState(false); //提示
  const [visible10, setVisible10] = useState(false); //提示
  const [hintEqu, setHintEqu] = useState(''); //提示
  const [accountStatus, setAccountStatus] = useState(0);
  const [progress, setProgress] = useState(0); //进度值
  const [second, setSecond] = useState(30); //计数值
  const [ShowsProgress, setShowsProgress] = useState(false); //进度动画
  const [ShowsDrag, setShowsDrag] = useState(true); //滑动
  const [lockInTimeAC, setlockInTimeAC] = useState(getCurrentDate());
  const touchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const inputRef2 = useRef<HTMLDivElement>(null);
  const [EquEleStatus, setEquEleStatus] = useState(false); //设备是否离线
  const [equVersion, setequVersion] = useState(''); //设备版本
  const [screenWidth, setScreenWidth] = useState(0); //屏幕宽
  const [isLoading, setIsLoading] = useState(true);
  const [equList, setEquList] = useState<any[]>([]); //设备列表
  const [userId, setUserId] = useState('');
  const [userName, setUserName] = useState('');
  const [form, setForm] = useState({
    seqNo: '',
    name: '',
    phone: '',
    carNo: '无',
    id: '',
  });

  const getBluetCode = () => {
    let cntStore = Taro.getStorageSync('BluetoothStore') || {};
    return BluetoothStore['电池编号'] || cntStore['电池编号'];
  };

  //数据加载Loading...
  const NotBluetoothGetBatteryData = () => {
    getStroageEquData();
    setEquEleStatus(false);
    getEquVersion();
    accountStatusFun();
    let setNo = Taro.getStorageSync('seqNo');
    OpenDialog();
    OpenDialogTwo();
    getEquData({ devicesn: setNo });
  };

  const getEquData = value => {
    getBatteryData(value)
      .then(res => {
        let list = {};
        if (
          res.data.length == 0 ||
          value.devicesn == '' ||
          res.msg == '暂无承载数据' ||
          res.data[0].eleStatus === 8
        ) {
          if (value.devicesn == '') {
          } else if (res.msg == '暂无承载数据') {
          } else if (res.data.length == 0) {
          } else if (res.data[0].eleStatus === 8) {
            setEquEleStatus(true);
          }
          store.BluetoothStore.setState(clearEquData);
          setisActiveImg(true);
          if (inputRef2.current) {
            inputRef2.current.style.backgroundColor = 'transparent';
          }
        } else {
          setlockInTimeAC(res.data[0].time);
          list = {
            剩余电量: res.data[0].resiEle,
            告警: res.data[0].alarm,
            效验: res.data[0].validate,
            温度: [
              res.data[0].temperature7,
              res.data[0].temperature6,
              res.data[0].temperature5,
              res.data[0].temperature4,
              res.data[0].temperature3,
              res.data[0].temperature2,
              res.data[0].temperature1,
            ],
            电池状态: res.data[0].eleStatus,
            电池电压: [
              res.data[0].batterVol8,
              res.data[0].batterVol7,
              res.data[0].batterVol6,
              res.data[0].batterVol5,
              res.data[0].batterVol4,
              res.data[0].batterVol3,
              res.data[0].batterVol2,
              res.data[0].batterVol1,
            ],
            电池编号: res.data[0].devicesn,
            电流: res.data[0].eleFlow,
            硬件状态: res.data[0].devStatus,
            累计循环放电次数: 0,
            软件版本: res.data[0].softVer,
            预计使用时间: res.data[0].estTime,
          };
          setisActiveImg(false);
          if (inputRef2.current) {
            inputRef2.current.style.backgroundColor = 'transparent';
          }
          store.BluetoothStore.setState(list);
        }
        disposalData();
        recordStore = disposalData()[0];
        deviceStore = disposalData()[1];
      })
      .then(() => {
        setIsLoading(true);
      });
  };

  //数据格式结构更改统一放这里
  let DianYaMax: any = 0;
  let WenDuMax: any = 0;
  const disposalData = () => {
    if (BluetoothStore['电池电压'].length >= 1) {
      let sum = 0;
      for (let i = 0; i < BluetoothStore['电池电压'].length; i++) {
        sum += parseFloat(BluetoothStore['电池电压'][i]);
      }
      DianYaMax = sum.toFixed(2);
    }

    if (BluetoothStore['温度'].length >= 1) {
      let WenArr = [
        BluetoothStore['温度'][3],
        BluetoothStore['温度'][4],
        BluetoothStore['温度'][5],
        BluetoothStore['温度'][6],
      ];
      if (Math.max(...WenArr) > 0) {
        WenDuMax = Math.max(...WenArr).toFixed(2); // 显示最大的值
      } else if (Math.min(...WenArr) < 0) {
        WenDuMax = Math.min(...WenArr).toFixed(2); // 显示最小的值
      }
    }
    if (DianYaMax == 0 || WenDuMax == 0) {
      DianYaMax = '--';
      WenDuMax = '--';
    }
    const recordStore = [
      { label: '电压', value: `${DianYaMax}V`, icon: Icon1 },
      { label: '电流', value: `${BluetoothStore['电流']}A`, icon: Icon2 },
      { label: '电池温度', value: `${WenDuMax}℃`, icon: Icon3 },
      // { label: '循环次数', value: BluetoothStore['累计循环放电次数'], icon: Icon4 },
      {
        label: '智能加热',
        value: `${BluetoothStore['温度'][1] == 200 || BluetoothStore['温度'][1] == undefined ? '--' : BluetoothStore['温度'][1]}℃`,
        icon: BluetoothStore['硬件状态'][0] == '1' ? Fire : FireTwo,
      },
    ];
    const deviceStore = [
      { label: '电池编号', value: getBluetCode(), icon: Icon8 },
      // { label: '设备名称', value: counterStore.localName, icon: Icon5 },
      // { label: '设备状态', value: getNotifyStatus[BluetoothStore['电池状态']], icon: Icon6 },
      // { label: '预计使用时间', value: BluetoothStore['预计使用时间'], icon: Icon7 },
    ];
    return [recordStore, deviceStore];
  };
  let recordStore = disposalData()[0];
  let deviceStore = disposalData()[1];

  //检测网络开关
  const onNetwork = () => {
    Taro.onNetworkStatusChange(res => {
      if (res.isConnected == true) {
        NotBluetoothGetBatteryData();
      } else if (
        isBluetoothLink.bluetoothLink == false &&
        res.isConnected === false
      ) {
        Taro.showToast({
          title: '当前未连接网络',
          icon: 'error',
        });
        setisActiveImg(true);
        if (inputRef2.current) {
          inputRef2.current.style.backgroundColor = '#eef0f37c';
        }
        store.BluetoothStore.setState(clearEquData);
      }
    });
  };

  useEffect(() => {
    const InfoSync = Taro.getDeviceInfo();
    if (counterStore.readId) {
      if (InfoSync.platform === 'android') {
        Taro.setBLEMTU({
          ...counterStore,
          mtu: 156,
          success: res => {
            notifyBLECharacteristicValueChange(counterStore);
          },
          fail: res => {
            Taro.closeBLEConnection({
              deviceId: counterStore.deviceId,
              success: () => {
                Taro.showToast({
                  title: '已断开连接',
                });
                Taro.redirectTo({
                  url: '/pages/bluetooth/index',
                });
              },
            });
          },
        });
        // })
      } else {
        notifyBLECharacteristicValueChange(counterStore);
      }
    } else {
      let phone = Taro.getStorageSync('phone');
      if (phone) {
      } else {
        return;
      }
    }
  }, [store.counterStore.state]);

  //颜色响应
  (useEffect(() => {
    //这里报红可以不管
    if (isBluetoothLink.bluetoothLink === true) {
      setisActiveImg2(false);
      setisActiveImg(true);
      if (inputRef.current && inputRef2.current) {
        inputRef.current.style.backgroundColor = 'transparent';
        inputRef2.current.style.backgroundColor = 'transparent';
      }
    } else if (isBluetoothLink.bluetoothLink === false) {
      setisActiveImg2(true);
      if (inputRef.current) {
        inputRef.current.style.backgroundColor = 'transparent';
      }
    }
  }),
    [isBluetoothLink.bluetoothLink]);

  //用户提示
  const OpenDialog = () => {
    if (!phone) return;
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      if (phone && res.data.seqNo == '' && visible10 == false) {
        setVisible1(true);
      } else if (phone && res.data.seqNo !== '') {
        setVisible1(false);
      }
    });
  };

  //用户提示
  const closeNoticeBar = () => {
    setVisible1(false);
  };

  //用户提示
  const OpenDialogTwo = () => {
    if (!phone) {
      setVisible2(true);
    } else if (phone) {
      setVisible2(false);
    }
  };

  (useEffect(() => {
    const refresh1 = Taro.getStorageSync('refresh');
    if (refresh1) {
      NotBluetoothGetBatteryData();
      Taro.setStorageSync('refresh', false);
    }
  }),
    [refresh]);

  //设备切换响应
  useEffect(() => {
    if (refreshStore.refreshCount > 0 && !isBluetoothLink.bluetoothLink) {
      NotBluetoothGetBatteryData();
    }
  }, [refreshStore.refreshCount]);

  //蓝牙--->4G
  useEffect(() => {
    if (
      refreshStore.blueToFourCount > 0 &&
      !isBluetoothLink.bluetoothLink &&
      Taro.getStorageSync('toFour')
    ) {
      NotBluetoothGetBatteryData();
      bluetoothToFourHint();
      Taro.removeStorageSync('toFour');
    }
  }, [refreshStore.blueToFourCount]);

  //蓝牙
  const notifyBLECharacteristicValueChange = counterStore => {
    console.log('可以读取的特征值');
    Taro.notifyBLECharacteristicValueChange({
      // 启用监听设备特征值变化
      state: true, // 启用 notify 功能
      ...counterStore,
      characteristicId: counterStore.readId,
      success(res) {
        onBLECharacteristicValueChange();
      },
      fail: err => {
        console.log(err, '启用监听失败');
      },
    });
  };

  //蓝牙
  const onBLECharacteristicValueChange = () => {
    // ArrayBuffer转16进制字符串示例
    function ab2hex(buffer) {
      let hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
          return ('00' + bit.toString(16)).slice(-2);
        }
      );
      return hexArr.join('');
    }
    Taro.onBLECharacteristicValueChange(res => {
      const date = ab2hex(res.value);
      const NotifyRecord = getParseNotifyRecord(date);
      NotifyRecord['电池编号'] = (
        'BAT' + NotifyRecord['电池编号']
      ).toUpperCase();
      setHintEqu(NotifyRecord['电池编号']);
      bluetoothToFourHint(NotifyRecord['电池编号']);
      NotifyRecord['电池电压'] = NotifyRecord['电池电压'].reverse();
      Taro.setStorageSync('BluetoothStore', NotifyRecord);
      //蓝牙数据过来蓝牙已经连上
      store.bluetoothLink.setState(true);
      setlockInTimeAC(getCurrentDate());
      store.BluetoothStore.setState(NotifyRecord);
      setEquEleStatus(false);
    });
  };
  //蓝牙
  const writeBLECharacteristicValue = (buffer, fn) => {
    Taro.writeBLECharacteristicValue({
      ...counterStore,
      characteristicId: counterStore.writeDefaultId,
      value: buffer,
      writeType: 'write',
      success: res => {
        console.log('成功了了了');
        fn && fn();
      },
      fail: err => {
        console.log('失败了了了了');
        Taro.showToast({
          title: '操作失败!',
          duration: 2000,
        });
      },
    });
  };
  //蓝牙下发（添加防抖，300ms间隔）
  const handleReset = debounce((e, type) => {
    if (isBluetoothLink.bluetoothLink == true) {
      let buffer;
      // 向蓝牙设备发送一个0x00的16进制数据
      const buffers = new ArrayBuffer(3);
      let dataView = new DataView(buffers);
      if (type === 1) {
        dataView.setUint8(0, 0xe3);
        dataView.setUint8(1, 0x01);
        dataView.setUint8(2, 0x1c);
        buffer = buffers;
      } else if (type === 2) {
        //蓝牙强启
        dataView.setUint8(0, 0xe3);
        dataView.setUint8(1, 0x02);
        dataView.setUint8(2, 0x1c);
        buffer = buffers;
      } else {
        dataView.setUint8(0, 0xe3);
        dataView.setUint8(1, 0x03);
        dataView.setUint8(2, 0x1c);
        buffer = buffers;
      }
      //suc
      writeBLECharacteristicValue(buffer, () => {
        if (type === 1) {
          Taro.showToast({
            title: '操作成功!',
            icon: 'success',
            duration: 2000,
          });
        }
        if (type === 2) {
          //强启指令成功
          if (Taro.getStorageSync('direct')) {
            Taro.removeStorageSync('direct');
            Taro.showToast({
              title: '操作成功!',
              icon: 'success',
              duration: 2000,
            });
          } else {
            onIsOverlay();
          }
        }
        if (type === 3) {
          Taro.showToast({
            title: '操作成功!',
            icon: 'success',
            duration: 2000,
          });
        }
      });
    } else if (isBluetoothLink.bluetoothLink == false) {
      let setNo = Taro.getStorageSync('seqNo');
      if (type == 1) {
        !e
          ? getwxSendComC8Is(setNo, 'A7', 'C8')
          : getwxSendComC8Is(setNo, 'A8', 'C8');
      } else if (type == 2) {
        !e ? getwxSendComC8Is(setNo, 'A6', 'C8') : '确定关闭强启吗?';
      } else {
        getwxSendComC8Is(setNo, 'A5', 'C8');
      }
    }
  }, 500);
  //4G下发
  const getwxSendComC8Is = (seqNo, batNo, type) => {
    //A5关机 A6强启 A7开启加热功能 A8关闭自动加热功能
    if (accountStatus != 2) return;
    //下发限制低于"1014"
    if (equVersion < '1014' && batNo == 'A5') {
      Taro.showModal({
        title: '温馨提示',
        content: '当前设备不支持远程关机，请走近设备使用蓝牙关机',
        showCancel: false,
      });
      return;
    }
    if (equVersion < '1014' && batNo == 'A6') {
      Taro.showModal({
        title: '温馨提示',
        content: '当前设备不支持远程强启，请走近设备使用蓝牙强启',
        showCancel: false,
      });
      return;
    }
    //生成12位随机数
    const randomNum = Math.floor(Math.random() * *************)
      .toString()
      .padStart(12, '0');
    if (batNo !== 'A9') {
      Taro.showLoading({
        title: '',
        mask: true,
      });
    }
    getwxSendComC8({
      batNo: seqNo,
      uuid: randomNum,
      dataType: type,
      filedVal: batNo,
    })
      .then(res => {
        if (randomNum !== res.data.uuid) return;
        if (batNo == 'A9' && res.data.error) {
          Taro.removeStorageSync('NoLoading');
          return;
        } else if (res.data.error) {
          Taro.hideLoading();
          Taro.showToast({
            title: '设备指令超时!',
            icon: 'error',
            duration: 2000,
          });
        } else {
          return res;
        }
      })
      .then(res => {
        const IsSuccess = JSON.parse(res.data.data);
        const clear = setTimeout(() => {
          Taro.removeStorageSync('NoLoading');
          if (IsSuccess.Data == 1) {
            if (batNo == 'A9') {
            } else {
              Taro.hideLoading();
            }
            if (batNo == 'A6') {
              //开启强启
              if (Taro.getStorageSync('direct')) {
                Taro.removeStorageSync('direct');
                Taro.showToast({
                  title: '操作成功',
                  icon: 'success',
                  duration: 2000,
                });
              } else {
                Taro.showToast({
                  title: '请等待30S',
                  icon: 'error',
                  duration: 2000,
                });
                onIsOverlay();
              }
            }
            NotBluetoothGetBatteryData();
          } else {
            if (batNo == 'A9') {
            } else {
              Taro.hideLoading();
              Taro.showToast({
                title: '操作失败!',
                icon: 'error',
                duration: 2000,
              });
            }
          }
          clearTimeout(clear);
        }, 5000);
      })
      .catch(err => {
        NotBluetoothGetBatteryData();
        console.log(err, '输出错误？');
      });
  };
  //.....
  const getStroageEquData = () => {
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      setForm({
        ...res.data,
      });
      setUserName(res.data.realName);
      setUserId(res.data.id);
      Taro.setStorageSync('seqNo', res.data.seqNo);
    });
  };

  //版本
  const getEquVersion = () => {
    let setNo = Taro.getStorageSync('seqNo');
    getDeviceVersion({ devicesn: setNo }).then(res => {
      setequVersion(res.data.softVer);
    });
  };

  //状态账号
  const accountStatusFun = () => {
    if (!Taro.getStorageSync('phone')) {
      setAccountStatus(0);
    } else if (
      Taro.getStorageSync('phone') &&
      (Taro.getStorageSync('seqNo') == '' ||
        Taro.getStorageSync('seqNo') == null)
    ) {
      setAccountStatus(1);
    } else if (Taro.getStorageSync('phone') && Taro.getStorageSync('seqNo')) {
      setAccountStatus(2);
    }
  };

  //蓝牙-->4G
  const IsOpen4GDio = () => {
    if (Taro.getStorageSync('Open4GDio')) {
      setVisible3(true);
      Taro.removeStorageSync('Open4GDio');
    }
  };
  //蓝牙-->4G
  const GotoBing = () => {
    setVisible3(false);
    Taro.setStorageSync('FromToBluetooth', true);
    refreshStore.clickEwm();
  };
  //蓝牙-->4G
  const CloseVisible3Dialog = () => {
    setVisible3(false);
    Taro.removeStorageSync('BluetoothEqu');
  };
  //进度条颜色
  const gradientColor = {
    '0%': '#FF5E5E',
    '100%': '#FFA062',
  };
  //强启动画 关
  const closeIsOverlay = () => {
    setShowsProgress(false);
    setProgress(0);
    cleanupFn();
  };
  //强启动画 开
  const onIsOverlay = () => {
    setShowsProgress(true);
    setProgress(0);
    const cleanup = setAddProgressVal(3.3);
    setCleanupFn(() => cleanup);
    return cleanup;
  };
  //强启动画
  const setAddProgressVal = setValue => {
    const timer = setInterval(() => {
      setProgress(prev => {
        const newValue = prev + setValue;
        const newValue2 = Math.round((99 - newValue) / 3.3);
        setSecond(newValue2);
        if (newValue >= 99) {
          clearInterval(timer);
          closeIsOverlay();
          Taro.showToast({
            title: '操作成功!',
            icon: 'success',
            duration: 2000,
          });
          setSecond(30);
          return 100;
        }
        return newValue;
      });
    }, 1000);
    return () => clearInterval(timer);
  };

  const DragStyle: React.CSSProperties = {
    borderRadius: '57px',
    height: '51px', //移动范围
    width: ` ${screenWidth * 0.49 + 'px'}`, //移动范围
    lineHeight: '51px',
    textAlign: 'center',
    position: 'absolute',
    // backgroundColor:'#1b77ad86',
    top: '50%',
    transform: 'translateY(-50%)',
    // zIndex: '2001',
    color: 'black',
  };

  const rowStyle: React.CSSProperties = {
    width: '60%',
    borderRadius: '57px',
  };

  const ImageTwo = {
    width: '75rpx',
    height: '75rpx',
    marginLeft: '0px',
  };
  //范围
  const range = {
    top: 0,
    left: Taro.getWindowInfo().screenWidth * 0.423,
    bottom: 0,
    right: Taro.getWindowInfo().screenWidth * 0.13,
  };

  const btnStyle3: React.CSSProperties = {
    borderRadius: '57px',
    fontSize: '14px',
    color: '#fff',
    width: '8.3vh',
    height: '8.3vh',
    lineHeight: '47px',
    backgroundColor: 'white',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    top: '8%',
    left: '11%',
  };

  const range1 = {
    top: 0,
    left: Taro.getWindowInfo().screenWidth * 0.2,
    bottom: 0,
    right: Taro.getWindowInfo().screenWidth * 0.2,
  };

  //滑动动画
  const getScreenWidth = () => {
    const systemInfo = Taro.getWindowInfo();
    setScreenWidth(systemInfo.screenWidth);
    return systemInfo.screenWidth;
  };
  //滑动动画
  const handleTouchMove = e => {
    if (
      (e.touches[0].pageX > screenWidth * 0.85 &&
        !EquEleStatus &&
        accountStatus == 2) ||
      (e.touches[0].pageX > screenWidth * 0.85 &&
        isBluetoothLink.bluetoothLink == true &&
        !EquEleStatus)
    ) {
      //这里来打开遮罩层 用该遮罩层来写这个苹果的关机
      OpenVisible4Dialog();
      setVisible8(true);
    }
    if (
      e.touches[0].pageX < screenWidth * 0.5 &&
      EquEleStatus &&
      accountStatus == 2
    ) {
      //打开强启弹窗
      OpenEqu();
    }
  };
  const handleTouchMove1 = e => {
    if (
      e.touches[0].pageX > screenWidth * 0.7 &&
      !EquEleStatus &&
      accountStatus == 2
    ) {
      //下发关机指令
      closeVisible4Dialog();
      setVisible8(false);
      handleReset(true, 3);
    }
  };
  //滑动动画
  const OpenVisible4Dialog = () => {
    setVisible4(true);
    setShowsDrag(false);
  };
  //滑动动画
  const closeVisible4Dialog = () => {
    setVisible4(false);
    setVisible8(false);
    setShowsDrag(true);
  };
  //滑动开机
  const OpenEqu = () => {
    handleReset(true, 2);
    Taro.setStorageSync('direct', true);
  };

  //下拉刷新
  const loadData = async () => {
    try {
      Taro.setStorageSync('closeVisible10', true);
      NotBluetoothGetBatteryData();
      if (Taro.getStorageSync('NoLoading')) return;
      Taro.setStorageSync('NoLoading', true);
      // 你的数据请求逻辑
      if (isBluetoothLink.bluetoothLink === false) {
        getwxSendComC8Is(seqNo, 'A9', 'C9');
      }
    } finally {
      Taro.stopPullDownRefresh(); // 停止下拉刷新动画
    }
  };

  // 监听下拉刷新动作
  usePullDownRefresh(() => {
    loadData();
  });

  //获取列表
  const getBindBatterList = (value = {}) => {
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      getBatterAll({ userId: res.data.id }).then(res => {
        setEquList(res.data);
        Taro.setStorageSync('equList', res.data);
        if (res.data.length >= 1 && value == 'auto') {
          switchEqu(res.data[0].deviceNo);
        }
      });
    });
  };
  //打开列表
  const OpenEquList = () => {
    setVisible5(true);
    getBindBatterList();
  };

  //切换设备
  const switchEqu: any = seqNo => {
    onBind(seqNo);
    setVisible5(false);
  };

  //绑定
  const onBind: any = seqNo => {
    if (seqNo) {
      //切换
      postUpBindEqu({ id: userId, seqNo: seqNo });
    }
  };
  //切换
  const postUpBindEqu = form => {
    postBladeSystemUpBindEqu(form).then(res => {
      if (res.msg == '设备号已经绑定，请重新输入设备号') return;
      setForm({
        ...res.data,
      });
      Taro.setStorageSync('seqNo', res.data.seqNo);
      //检查蓝牙，打开了就执行
      Taro.getBluetoothAdapterState({
        success: function (state) {
          if (state.available) {
            publicBluetoothService();
          }
        },
      });
      store.BluetoothStore.setState(clearEquData);
      refreshStore.triggerRefresh();
      refreshStore.triggerDeviceSwitch();
      getBindBatterList();
      OpenDialog();
    });
  };
  //添加
  const postUpBindEqu1 = from => {
    userBindDevice({ userId: from.userId, deviceNo: from.seqNo }).then(
      resTwo => {
        //验证1
        if (resTwo.msg == '当前设备已经被绑定') return;
        postBladeSystemUpBindEqu({ id: from.userId, seqNo: from.seqNo })
          .then(res => {
            setForm({
              ...res.data,
            });
            Taro.setStorageSync('seqNo', res.data.seqNo);
            refreshStore.triggerRefresh();
            refreshStore.triggerDeviceSwitch();
            //刷新数据
            NotBluetoothGetBatteryData();
            getBindBatterList();
            if (Taro.getStorageSync('Ewm')) {
              setVisible7(true);
            }
            Taro.removeStorageSync('Ewm');
          })
          .catch(err => {
            liftUserBindEqu({ userId: userId, deviceNo: from.seqNo }).then(
              res => {
                console.log(res, '删除成功？');
              }
            );
          });
      }
    );
  };
  //弹窗
  const unbind1 = () => {
    setVisible6(true);
  };
  //解绑
  const onUnbind: any = () => {
    setVisible6(false);
    postBladeSystemUpUnBindEqu({ seqNo: form.seqNo, id: form.id })
      .then(res => {
        Taro.removeStorageSync('seqNo');
      })
      .then(() => {
        removeUserBindDevice(form);
        refreshStore.triggerRefresh();
        refreshStore.triggerDeviceSwitch();
        getBindBatterList('auto');
      });
  };
  //删除列表
  const removeUserBindDevice = form => {
    liftUserBindEqu({ userId: userId, deviceNo: form.seqNo }).then(res => {
      console.log('删除成功？', res);
    });
  };

  //删除列表1
  const removeUserBindDevice1 = () => {
    setVisible9(false);
    removeUserBindDevice({ seqNo: Taro.getStorageSync('isdelete') });
    getBindBatterList();
    Taro.removeStorageSync('isdelete');
  };

  //打开弹窗9
  const OpenVisible9 = value => {
    setVisible9(true);
    Taro.setStorageSync('isdelete', value);
  };

  //添加设备到用户列表以及绑定
  const addUserEquList = value => {
    getBladeSystemUserValidatePhone({ phone: phone }).then(res => {
      const regex = /^BAT[A-Z0-9]{8}$/;
      if (regex.test(value)) {
        postUpBindEqu1({ userId: res.data.id, seqNo: value });
      } else if (!regex.test(value)) {
        Taro.showToast({
          title: `编号格式错误`,
          icon: 'error',
          duration: 2000,
        });
        Taro.removeStorageSync('Ewm');
      }
    });
  };

  //扫码--添加列表--添加用户
  const tapScan = async () => {
    if (!phone) {
      Taro.redirectTo({
        url: `/pages/register/index`,
      });
      return;
    }
    Taro.scanCode({
      success: function (res) {
        const result = res.result;
        const response = getBladeSystemUserCkEquNoExist({ equNo: res.result });
        response.then(status => {
          if (status.success) {
            addUserEquList(result);
          }
        });
      },
      fail: function (res) {
        Taro.removeStorageSync('Ewm');
      },
    });
  };

  //前往连接蓝牙
  const GoToBlueTooth = () => {
    Taro.setStorageSync('autoBluetooth', false);
    Taro.redirectTo({
      url: '/pages/bluetooth/index?IsCustom=false',
    });
  };

  const closeVisible2Dialog = () => {
    setVisible7(false);
    Taro.removeStorageSync('Ewm');
  };

  const OpenPopover = () => {
    Taro.setStorageSync('OpenPopover', true);
    refreshStore.OpenPopoverCount();
    setVisible5(false);
  };
  //公共蓝牙服务功能
  const publicBluetoothService = async () => {
    if (Taro.getStorageSync('noBlueToothAgain')) {
      removeStorageSync('noBlueToothAgain');
    } else {
      // 初始化蓝牙适配器 调用蓝牙服务
      if (phone == '' || seqNo == '') {
        return;
      }
      //初始化 -- 搜索设备自动绑定
      const initResult = await bluetoothService.initBluetoothAdapter();
      if (initResult) {
        bluetoothService.startDiscovery();
      }
    }
  };

  //蓝牙 ---> 4G(设备绑定提示)
  const bluetoothToFourHint = (hintEqu = {}) => {
    const equList1 = Taro.getStorageSync('equList');
    if (Taro.getStorageSync('closeVisible10') == false) {
      return;
    }
    if (equList1.length >= 1) {
      //判断有无该设备
      let isHave = 0;
      setVisible1(false);
      equList1.forEach(res => {
        if (res.deviceNo == hintEqu && hintEqu !== '') {
          isHave++;
        }
      });
      //设备列表有没有该设备
      // if( isHave == 0){
      //   setVisible10(true);
      // }else{
      //   setVisible10(false);
      // }
      isHave == 0 ? setVisible10(true) : setVisible10(false);
    } else {
      //没有绑定设备
      setVisible10(true);
      setVisible1(false);
    }
    if (isBluetoothLink.bluetoothLink == false) {
      setVisible10(false);
      setVisible1(true);
    }
  };

  //拿到蓝牙数据hintEqu值是蓝牙连接设备编号值
  const getHintEqu = () => {
    postUpBindEqu1({ userId: userId, seqNo: hintEqu });
  };

  //蓝牙数据格式转化为接口数据格式
  const transforBlueData = async () => {
    //这里需要上传定位,不做其他的提示以及权限请求
    const setting = await Taro.getSetting();
    const locationRes = await Taro.getLocation({
      type: 'wgs84',
      isHighAccuracy: true, // 开启高精度定位
    });
    //获取当前蓝牙数据
    const BluetoothStore1 = store.BluetoothStore.state;
    let batterVolSum = 0;
    BluetoothStore1['电池电压'].forEach(ele => {
      batterVolSum += parseFloat(ele);
    });
    const VolSum = batterVolSum.toFixed(3).toString();
    //转化格式
    const FormatBlueData = {
      devicesn:
        BluetoothStore1['电池编号'] == '--' ? '' : BluetoothStore1['电池编号'],
      estTime: BluetoothStore1['预计使用时间'],
      softVer:
        BluetoothStore1['软件版本'] == '--' ? '' : BluetoothStore1['软件版本'],
      devStatus:
        BluetoothStore1['硬件状态'] == '--' ? '' : BluetoothStore1['硬件状态'],
      batterNo:
        BluetoothStore1['电池编号'] == '--' ? '' : BluetoothStore1['电池编号'],
      eleFlow: BluetoothStore1['电流'] == '--' ? '' : BluetoothStore1['电流'],
      resiEle:
        BluetoothStore1['剩余电量'] == '--' ? '' : BluetoothStore1['剩余电量'],
      validate: '4',
      batterVol1: BluetoothStore1['电池电压'][0],
      batterVol2: BluetoothStore1['电池电压'][1],
      batterVol3: BluetoothStore1['电池电压'][2],
      batterVol4: BluetoothStore1['电池电压'][3],
      batterVol5: BluetoothStore1['电池电压'][4],
      batterVol6: BluetoothStore1['电池电压'][5],
      batterVol7: BluetoothStore1['电池电压'][6],
      batterVol8: BluetoothStore1['电池电压'][7],
      batterVolAll: VolSum,
      temperature1: BluetoothStore1['温度'][6],
      temperature2: BluetoothStore1['温度'][5],
      temperature3: BluetoothStore1['温度'][4],
      temperature4: BluetoothStore1['温度'][3],
      temperature5: BluetoothStore1['温度'][2],
      temperature6: BluetoothStore1['温度'][1],
      temperature7: BluetoothStore1['温度'][0],
      sumCycEle:
        BluetoothStore1['累计循环放电次数'] == '--'
          ? ''
          : BluetoothStore1['累计循环放电次数'],
      alarm:
        BluetoothStore1['硬件状态'] == '--' ? '' : BluetoothStore1['硬件状态'],
      eleStatus: 0,
      lot: setting.authSetting['scope.userLocation']
        ? locationRes.longitude
        : '',
      lat: setting.authSetting['scope.userLocation']
        ? locationRes.latitude
        : '',
    };
    return FormatBlueData;
  };

  //蓝牙数据上传给接口
  const TimerSaveBuletoothData = () => {
    //定时器
    const TimerId = setInterval(async () => {
      bluetoothLink.setState({
        WebSocketUpDateCount: bluetoothLink.state.WebSocketUpDateCount + 1,
      });
      if (bluetoothLink.state.WebSocketUpDateCount >= 6) {
        bluetoothLink.setState({ WebSocketUpDateCount: 0 });
        clearInterval(TimerId);
        return;
      }
      const getTransforBlueData = await transforBlueData();
      const Data = JSON.stringify(getTransforBlueData);
      saveBluetoothData(Data).then(res => {
        console.log(res, '上传成功！');
      });
      // Taro.showToast({
      //   title: `蓝牙数据上传${bluetoothLink.state.WebSocketUpDateCount}次`,
      //   icon: 'success',
      //   duration: 2000
      // })
    }, 10000);
  };

  //连接WebSocket
  const connectWebSocket = async () => {
    //连接成功 ---> 打开服务 ---> 认证信息 ---> 打开正常需要的监听器

    //打开webSocket服务
    var task = await Taro.connectSocket({
      url: 'wss://socket.sloctopus.com/socket',
      success: function () {
        console.log('连接成功');
      },
    });

    //打开连接并进行认证
    task.onOpen(function () {
      task.send({
        data: JSON.stringify({
          type: 'auth',
          role: 'miniProgram',
        }),
      });
    });

    //连接保持30秒一次
    task.onOpen(() => {
      setInterval(() => {
        task.send({
          data: JSON.stringify({
            type: 'heartbeat',
          }),
          success: () => console.log('发送数据？'),
        });
      }, 30000);
    });

    //监听消息
    task.onMessage(function (msg: any) {
      let Message = JSON.parse(msg.data);
      const currentBlueEqu = Taro.getStorageSync('BluetoothStore');
      let operationType = Message.command == 'getBluetoothData'; //查看发送过来的是否是上传数据的指令
      let blueStatus = bluetoothLink.state.bluetoothLink; //查看蓝牙是否是连接的状态
      let isEqu = currentBlueEqu['电池编号'] === Message.deviceNo; //蓝牙缓存的数据中的编号
      if (operationType && isEqu && blueStatus) {
        // Taro.showToast({
        //   title: '开始上传蓝牙数据',
        //   icon: 'success',
        //   duration: 2000
        // })
        TimerSaveBuletoothData();
      } else if ((operationType && !blueStatus) || (operationType && !isEqu)) {
        //错误发送消息上去
        task.send({
          data: JSON.stringify({
            type: 'wx_message',
            command: {
              content: '当前用户该设备没有连接蓝牙',
            },
          }),
        });
      }
    });

    //发生错误
    task.onError(function (err) {
      console.log('onError', 'WebSocket发生错误');
      if (Taro.getSystemInfoSync().platform === 'android') {
        setTimeout(connectWebSocket, 3000);
      }
    });

    //连接关闭
    task.onClose(function (e) {
      console.log('onClose: ', e, 'WebSocket连接已关闭');
      connectWebSocket();
    });
  };

  useEffect(() => {
    if (Taro.getStorageSync('Ewm')) {
      tapScan(); //扫码
    }
    if (Taro.getStorageSync('FromToBluetooth')) {
      addUserEquList(Taro.getStorageSync('BluetoothEqu')); //蓝牙
      //绑定成功清除不必要的数据
      Taro.removeStorageSync('FromToBluetooth');
      Taro.removeStorageSync('BluetoothEqu');
    }
  }, [refreshStore.clickEwmCount]);

  // 初始加载
  useEffect(() => {
    Taro.setStorageSync('autoBluetooth', true);
    getBindBatterList();
    getScreenWidth();
    onNetwork();
    IsOpen4GDio();
    NotBluetoothGetBatteryData();
    publicBluetoothService();
    connectWebSocket();
  }, []);

  return (
    <View className='home-page'>
      <HeaderNavBar title='首页' />

      <Overlay visible={visible4}>
        {visible8 && (
          <View className='appleClose'>
            <View className='linearText'> 滑动来关闭电池</View>
            <Drag
              direction='x'
              boundary={range1}
              style={{ top: '0px', left: '0px' }}
            >
              <div style={btnStyle3} onTouchMove={handleTouchMove1}>
                <Image src={closeThree} className='slidePngStyle' />
              </div>
            </Drag>
            <View className='NoticeBarPosition'></View>
          </View>
        )}
        <View className='close1' onClick={() => closeVisible4Dialog()}>
          {' '}
          <Image src={NoPng} className='slidePngStyle1' />
        </View>
        <View className='appleCloseTitle2'>取消</View>
      </Overlay>

      {/* 蓝牙绑定跳转4G */}
      <Dialog
        title='提示'
        visible={visible3}
        onOk={() => GotoBing()}
        onCancel={() => CloseVisible3Dialog()}
      >
        是否获取4G数据?
      </Dialog>

      {/* 提示 */}
      {visible1 && (
        <NoticeBar
          color='white'
          background='#FB5151'
          closeMode
          onClose={closeNoticeBar}
        >
          <View className='hintHurdle'>
            <View>
              当前用户未绑定设备，无法获取网络数据.点击“+”的扫码去绑定
            </View>
          </View>
        </NoticeBar>
      )}

      {/* 提示 */}
      {visible2 && (
        <NoticeBar color='white' background='#FB5151'>
          <View className='hintHurdle'>
            {' '}
            <View>当前用户未登录</View>{' '}
            <View
              className='GotoBing'
              onClick={() => Taro.redirectTo({ url: `/pages/register/index` })}
            >
              {' '}
              去登录
            </View>
          </View>
        </NoticeBar>
      )}

      {/* 提示 */}
      {visible10 && (
        <NoticeBar color='white' background='#FB5151'>
          <View className='hintHurdle'>
            <View className='hintHurdle-tittle'>当前设备未绑定4G,请绑定</View>
            <View className='BlueGotoBing' onClick={() => getHintEqu()}>
              是
            </View>
            <View
              className='BlueGotoBing'
              onClick={() => {
                setVisible10(false);
                Taro.setStorageSync('closeVisible10', false);
              }}
            >
              否
            </View>
          </View>
        </NoticeBar>
      )}

      {/* 提示 */}
      <View className='TopzIndex'>
        <Dialog
          title='提示'
          visible={visible6}
          onOk={() => onUnbind()}
          onCancel={() => setVisible6(false)}
          // mask={false}
        >
          是否确定解绑？
        </Dialog>
      </View>

      {/* 提示 */}
      <View className='TopzIndex'>
        <Dialog
          title='提示'
          visible={visible9}
          onOk={() => {
            removeUserBindDevice1();
            getBindBatterList('auto');
          }}
          onCancel={() => {
            setVisible9(false);
            Taro.removeStorageSync('isdelete');
          }}
        >
          是否确定解绑？
        </Dialog>
      </View>

      {/* 设备绑定列表 */}
      <Dialog
        visible={visible5}
        onCancel={() => setVisible5(false)}
        noOkBtn={true}
        noCancelBtn={true}
        closeOnClickOverlay={true}
      >
        {/* 设备列表 */}
        <View className='equList'>
          {equList.map((device, index) => {
            const isCurrentDevice =
              device.deviceNo === Taro.getStorageSync('seqNo');
            if (isCurrentDevice) {
              return (
                //当前绑定设备
                <View className='switchList BindingSwitchList'>
                  <View className='userInfo' onClick={() => setVisible5(false)}>
                    <View className='Name'> {userName} </View>
                    <View className='deviceNo'>{device.deviceNo}</View>
                  </View>
                  <View className='onUnbindBtn' onClick={() => unbind1()}>
                    <Image src={unbind} />
                  </View>
                </View>
              );
            } else {
              return (
                //非当前绑定设备
                <View className='switchList'>
                  <View
                    className='userInfo'
                    onClick={() => switchEqu(device.deviceNo)}
                  >
                    <View className='Name'> {userName} </View>
                    <View className='deviceNo'>{device.deviceNo}</View>
                  </View>
                  <View
                    className='onUnbindBtn'
                    onClick={() => {
                      OpenVisible9(device.deviceNo);
                      postUpBindEqu({ id: userId, seqNo: device.deviceNo });
                    }}
                  >
                    <Image src={unbind} />
                  </View>
                </View>
              );
            }
          })}
          <Button
            type='primary'
            size='large'
            color='#4EBBB4'
            onClick={() => OpenPopover()}
          >
            <Image style={{ width: '20px', height: '20px' }} src={binding} />
            绑定新设备
          </Button>
        </View>
      </Dialog>

      <Dialog
        title='提示'
        visible={visible7}
        onOk={() => GoToBlueTooth()}
        onCancel={() => closeVisible2Dialog()}
      >
        是否连接蓝牙?
      </Dialog>

      <View className='home-page-header'>
        <View className='home-page-rowbtn'>
          <View className='home-page-rowbtn-btn1'>
            {deviceStore.map(items => {
              return (
                <View className='btnContent'>
                  <View className='userName'>{userName}</View>
                  <View className='EquIdCode' onClick={() => OpenEquList()}>
                    {items.value} <Image src={arrows} />
                  </View>
                </View>
              );
            })}
          </View>
          <View className='btn2List'>
            <View className='home-page-rowbtn-btn2' ref={inputRef2}>
              <View>
                <Image
                  src={isActiveImg ? Icon12 : Icon14}
                  className='home-page-content-img'
                />
              </View>
            </View>
            <View className='home-page-rowbtn-btn2' ref={inputRef}>
              <View className='lanya'>
                <Image
                  src={isActiveImg2 ? Icon11 : Icon13}
                  className='home-page-content-img lanya1'
                />
              </View>
            </View>
          </View>
        </View>
        <View className='lockInTime'>更新于：{lockInTimeAC}</View>
        <View className='home-page-row'>
          <View className='home-page-quantity'>
            <CircularProgress
              size={100}
              className='home-page-quantity-circle'
              // style={{ width: '100%', height: '100%' }}
              progress={
                BluetoothStore['剩余电量'] == '--'
                  ? 0
                  : BluetoothStore['剩余电量']
              }
              strokeWidth={10}
              strokeColor='#4EBBB4'
            >
              <View className='home-page-quantity-t2'>
                {BluetoothStore['剩余电量']}
                {BluetoothStore['剩余电量'] != '--' && (
                  <span className='home-page-quantity-t3'>%</span>
                )}
              </View>
              <View className='home-page-quantity-t1'>
                <View className='batteryIcon'>
                  <Image src={battery} />
                </View>
                剩余电量
              </View>
              {getStatus[BluetoothStore['电池状态']] !== '在线' && (
                <View
                  className='home-page-quantity-t4'
                  style={{
                    color:
                      getStatus[BluetoothStore['电池状态']] === '在线'
                        ? 'green'
                        : 'red',
                  }}
                >
                  {getStatus[BluetoothStore['电池状态']]}
                </View>
              )}
            </CircularProgress>
          </View>
          <View className='home-page-record'>
            {recordStore.map(items => {
              return (
                <View className='home-page-record-row'>
                  <Image
                    src={items.icon}
                    className='home-page-record-row-img'
                  />
                  {items.label != '智能加热' && (
                    <View className='RowContent'>
                      <View className='home-page-record-row-value'>
                        {items.label}
                      </View>
                      <View className='home-page-record-row-label'>
                        {items.value}
                      </View>
                    </View>
                  )}
                  {/* 智能加热 */}
                  {items.label == '智能加热' && (
                    <View className='RowContent'>
                      <View className='home-page-record-row-value'>
                        {items.label}
                      </View>
                      <View
                        className={
                          BluetoothStore['温度'][1] <= 0 ? 'lowTem' : 'normal'
                        }
                      >
                        {items.value}
                      </View>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      </View>

      <View className='home-page-content-switch'>
        {ShowsProgress && <View className='Overlay1'></View>}
        {/* Boolean(+BluetoothStore['硬件状态'][2]) 强启的状态 */}
        <View
          className='home-page-content-switch-row'
          onClick={() => {
            handleReset(false, 2);
          }}
        >
          {ShowsProgress && (
            <View className='CircleProgress1'>
              <CircleProgress
                progress={progress}
                radius={38}
                circleColor={'#4EBBB4'}
              >
                {second + 's'}
              </CircleProgress>
            </View>
          )}
          {/* 低压闪烁 */}
          <View
            className={+BluetoothStore['告警'][1] ? 'lowTension' : 'titleBtn'}
          >
            <Image
              src={ShowsProgress ? ElectricityActive : Electricity}
              style={ImageTwo}
            />
          </View>
        </View>

        <View className='home-page-content-switch-row' style={rowStyle}>
          <View className='titleBtn'>
            {EquEleStatus && (
              <View className='leftClose'>
                <Image src={rightClosePng} className='rightClose' />
              </View>
            )}
            {!EquEleStatus && (
              <View className='rightClose'>
                <Image src={leftClosePng} className='leftClose' />
              </View>
            )}
            {ShowsDrag && (
              <View ref={touchRef} style={DragStyle}>
                {/* 关机状态 */}
                {EquEleStatus && (
                  <Drag
                    direction='x'
                    boundary={range}
                    style={{ left: '62.5%', top: '0px' }}
                  >
                    <div
                      className='touch-dom'
                      onTouchMove={handleTouchMove}
                      style={EquEleStatus ? btnStyle2 : btnStyle1}
                    >
                      <Image src={closeOne} className='slidePngStyle' />
                    </div>
                  </Drag>
                )}
                {/* 开机状态 */}
                {!EquEleStatus && (
                  <Drag
                    direction='x'
                    boundary={range}
                    style={{ left: '0px', top: '0px' }}
                  >
                    <div
                      className='touch-dom'
                      onTouchMove={handleTouchMove}
                      style={EquEleStatus ? btnStyle2 : btnStyle1}
                    >
                      <Image src={closeOne} className='slidePngStyle' />
                    </div>
                  </Drag>
                )}
              </View>
            )}
            {!ShowsDrag && (
              <View ref={touchRef} style={DragStyle}>
                操作中...
              </View>
            )}
          </View>
        </View>

        <View className='titleFont1'>一键强启</View>
        <View className='titleFont2'>
          {EquEleStatus ? '向左滑动开启设备' : '向右滑动关闭设备'}
        </View>
      </View>
    </View>
  );
});

export default inject(store => store)(HomePage);
