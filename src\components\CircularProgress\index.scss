.circular-progress {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &__svg {
    width: 100%;
    height: 100%;
  }

  &__bg {
    stroke: #ebedf0;
  }

  &__bar {
    transition: stroke-dashoffset 0.6s ease;
  }

  &__content {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}
.circular-progress__content {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  transform: translateY(-50%);
  text-align: center;
  color: #1a1a1a;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  gap: 4%;
}
